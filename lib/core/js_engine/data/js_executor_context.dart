/// JsExecutor 的上下文数据管理类
class JsExecutorContext {
  final Map<String, dynamic> _data = {};

  /// 设置数据
  void setData(JsExecutorContextConstants key, dynamic value) {
    _data[key.value] = value;
  }

  /// 获取数据
  dynamic getData(JsExecutorContextConstants key) {
    return _data[key.value];
  }

  /// 清除所有数据
  void clearAll() {
    _data.clear();
  }

  /// 清除指定键的数据
  void clear(JsExecutorContextConstants key) {
    _data.remove(key.value);
  }
}

/// 常量类
enum JsExecutorContextConstants {
  assetId('assetId');

  const JsExecutorContextConstants(this.value);
  final String value;

  @override
  String toString() => value;
}
