import 'package:asset_force_mobile_v2/core/js_engine/data/js_executor_context.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_turl_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/services/asset_detail_service.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/asset_print_able_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/function_permission_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_asset_history_records_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_customize_logic_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_layout_settings_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_relation_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_user_role_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/init_schedule_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/live_talk_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/load_asset_by_id_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/save_asset_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/controllers/asset_detail_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/states/asset_detail_ui_state.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/repositories/asset_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_type/domain/usecase/get_asset_type_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/appointment_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/asset_type_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/user_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/appointment_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/asset_type_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:get/get.dart';

class AssetDetailBinding extends Bindings {
  AssetDetailBinding();

  @override
  void dependencies() {
    final String id = Get.parameters['id'] ?? '';
    final assetDetailArguments = Get.arguments as AssetDetailArguments;

    Get.lazyPut(() => JsExecutorContext(), tag: id);
    final jsExecutorContext = Get.find<JsExecutorContext>(tag: id);

    LogUtil.d('AssetDetailBinding dependencies called with id: $id');
    final dioUtil = Get.find<DioUtil>();

    // state
    Get.create(() => AssetDetailUiState());

    // repository
    Get.lazyPut<UserRepository>(() => UserRepositoryImpl(dioUtil: dioUtil));
    Get.lazyPut<AssetRepository>(() => AssetRepositoryImpl(dioUtil: dioUtil));
    Get.lazyPut<AppointmentRepository>(() => AppointmentRepositoryImpl(dioUtil: dioUtil));
    Get.lazyPut<AssetTypeRepository>(() => AssetTypeRepositoryImpl(dioUtil: dioUtil));

    final AssetRepository assetRepository = Get.find<AssetRepository>();
    final AppointmentRepository appointmentRepository = Get.find<AppointmentRepository>();
    final UserRepository userRepository = Get.find<UserRepository>();
    final AssetTypeRepository assetTypeRepository = Get.find<AssetTypeRepository>();

    // use case
    Get.lazyPut(
      () => LoadAssetByIdUseCase(
        assetRepository: assetRepository,
        appointmentRepository: appointmentRepository,
        userRepository: userRepository,
      ),
    );

    Get.lazyPut(() => GetAssetTypeListUseCase(assetTypeRepository));
    Get.lazyPut(() => GetAssetHistoryRecordsUseCase(assetRepository: assetRepository));
    Get.lazyPut(() => GetRelationListUseCase(assetRepository: assetRepository));
    Get.lazyPut(() => AssetsPrintAbleUseCase(assetRepository: assetRepository));
    Get.lazyPut(() => LiveTalkUseCase());
    Get.lazyPut(() => GetRelationListUseCase(assetRepository: assetRepository));
    Get.lazyPut(() => SaveAssetUseCase(assetRepository: assetRepository));
    Get.lazyPut(() => InitScheduleUseCase(appointmentRepository));
    Get.lazyPut(() => GetLayoutSettingsUseCase(assetRepository: assetRepository));
    Get.lazyPut(() => FunctionPermissionUseCase(userRepository: userRepository));
    Get.lazyPut(() => GetCustomizeLogicUseCase(assetTypeRepository: assetTypeRepository));
    Get.lazyPut(() => GetUserRoleUseCaseUseCase(userRepository: userRepository));
    Get.lazyPut(() => GetTurlUseCase(s3Repository: Get.find<S3Repository>()));

    // service
    Get.lazyPut(
      () => AssetDetailService(
        loadAssetByIdUseCase: Get.find<LoadAssetByIdUseCase>(),
        getRelationListUseCase: Get.find<GetRelationListUseCase>(),
        saveAssetUseCase: Get.find<SaveAssetUseCase>(),
        initScheduleUseCase: Get.find<InitScheduleUseCase>(),
        getLayoutSettingsUseCase: Get.find<GetLayoutSettingsUseCase>(),
        functionPermissionUseCase: Get.find<FunctionPermissionUseCase>(),
        createLiveTalkUrlUseCase: Get.find<LiveTalkUseCase>(),
        assetsPrintAbleUseCase: Get.find<AssetsPrintAbleUseCase>(),
        getCustomizeLogicUseCase: Get.find<GetCustomizeLogicUseCase>(),
        getUserRoleUseCaseUseCase: Get.find<GetUserRoleUseCaseUseCase>(),
        getAssetHistoryRecordsUseCase: Get.find<GetAssetHistoryRecordsUseCase>(),
        getAssetTypeListUseCase: Get.find<GetAssetTypeListUseCase>(),
      ),
    );

    // controller
    Get.lazyPut(
      () => AssetDetailController(
        uiState: Get.find<AssetDetailUiState>(),
        assetDetailService: Get.find<AssetDetailService>(),
        assetDetailArguments: assetDetailArguments,
        navigationService: Get.find<NavigationService>(),
        jsExecutorContext: jsExecutorContext,
      ),
      tag: id,
    );
  }
}
