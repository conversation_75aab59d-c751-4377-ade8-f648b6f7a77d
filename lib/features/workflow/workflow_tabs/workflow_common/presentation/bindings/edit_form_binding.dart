import 'package:asset_force_mobile_v2/core/extensions/getx_extension.dart';
import 'package:asset_force_mobile_v2/core/js_engine/data/js_executor_context.dart';
import 'package:asset_force_mobile_v2/core/js_engine/js_engine.dart';
import 'package:asset_force_mobile_v2/core/services/get_navigation_service.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/calc_asset_dict_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/check_validate_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/prepare_asset_data_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/edit_form_controller.dart';
import 'package:get/get.dart';

class EditFormBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPutFenix(() => PrepareAssetDataUseCase());
    Get.lazyPut(() => GetNavigationService());
    Get.lazyPutFenix(() => CalcAssetDictUseCase(Get.find<JsExecutor>()));
    Get.lazyPutFenix(() => CheckValidateUseCase());

    // 注册 JsExecutor Context
    // 用于存储JsExecutor 执行过程中，临时存储页面数据
    Get.lazyPut(() => JsExecutorContext());

    final jsExecutorContext = Get.find<JsExecutorContext>();

    // 注册 JsBridgeFunctionRegistry
    Get.lazyPut(() => JsBridgeFunctionRegistry(), fenix: true);
    Get.lazyPut(() => JsBridgeDataManager(), fenix: true);
    Get.lazyPut(() => JsBridgeStateManager(), fenix: true);
    Get.lazyPut(() => JsBridgeUiManager(), fenix: true);
    Get.lazyPut(
      () => JsFlutterBridgeService(
        functionRegistry: Get.find<JsBridgeFunctionRegistry>(),
        dataManager: Get.find<JsBridgeDataManager>(),
        stateManager: Get.find<JsBridgeStateManager>(),
        uiManager: Get.find<JsBridgeUiManager>(),
        assetRepository: Get.find<AssetRepository>(),
        jsExecutorContext: jsExecutorContext,
      ),
      fenix: true,
    );
    Get.lazyPut(() => JsExecutor(Get.find<JsFlutterBridgeService>()));
    // 注册 JsBridgeFunctionRegistry 结束

    Get.lazyPutFenix(
      () => AfCustomizeViewController(
        prepareAssetDataUseCase: Get.find<PrepareAssetDataUseCase>(),
        calcAssetDictUseCase: Get.find<CalcAssetDictUseCase>(),
        checkValidateUseCase: Get.find<CheckValidateUseCase>(),
        navigationService: Get.find<GetNavigationService>(),
        jsExecutor: Get.find<JsExecutor>(),
        jsExecutorContext: jsExecutorContext,
      ),
    );
    Get.lazyPut(() => EditFormController(navigationService: Get.find<GetNavigationService>()));
  }
}
